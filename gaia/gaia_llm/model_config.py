"""
Centralized LLM Model Configuration

This module provides a single source of truth for all LLM models across the application.
All model lists, defaults, and metadata should be defined here and imported by other modules.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class ModelInfo:
    """Information about a specific LLM model."""
    id: str
    name: str
    provider: str
    release_date: Optional[str] = None
    capabilities: Optional[List[str]] = None
    context_length: Optional[int] = None
    is_default: bool = False
    is_deprecated: bool = False
    description: Optional[str] = None


class ModelRegistry:
    """Central registry for all LLM models."""
    
    def __init__(self):
        self._models = self._initialize_models()
    
    def _initialize_models(self) -> Dict[str, ModelInfo]:
        """Initialize the model registry with all available models."""
        models = {}
        
        # Anthropic Models (ordered by preference/recency)
        anthropic_models = [
            ModelInfo(
                id="claude-opus-4-20250514",
                name="Claude Opus 4",
                provider="anthropic",
                release_date="2025-05-14",
                capabilities=["tools", "vision", "analysis"],
                context_length=200000,
                description="Most capable Claude 4 model for complex tasks"
            ),
            ModelInfo(
                id="claude-sonnet-4-20250514",
                name="Claude Sonnet 4",
                provider="anthropic",
                release_date="2025-05-14",
                capabilities=["tools", "vision", "analysis"],
                context_length=200000,
                is_default=True,
                description="Smart, efficient Claude 4 model for everyday use"
            ),
            ModelInfo(
                id="claude-haiku-4-20250514",
                name="Claude Haiku 4",
                provider="anthropic",
                release_date="2025-05-14",
                capabilities=["tools", "vision", "analysis"],
                context_length=200000,
                description="Fast, lightweight Claude 4 model for quick tasks"
            ),
            ModelInfo(
                id="claude-3-5-sonnet-20241022",
                name="Claude 3.5 Sonnet (Latest)",
                provider="anthropic",
                release_date="2024-10-22",
                capabilities=["tools", "vision", "analysis"],
                context_length=200000,
                description="Latest Claude 3.5 Sonnet with improved capabilities"
            ),
            ModelInfo(
                id="claude-3-5-haiku-20241022",
                name="Claude 3.5 Haiku (Latest)",
                provider="anthropic",
                release_date="2024-10-22",
                capabilities=["tools", "fast"],
                context_length=200000,
                description="Latest Claude 3.5 Haiku - fast and efficient"
            ),
            ModelInfo(
                id="claude-3-5-sonnet-20240620",
                name="Claude 3.5 Sonnet",
                provider="anthropic",
                release_date="2024-06-20",
                capabilities=["tools", "vision", "analysis"],
                context_length=200000,
                description="Claude 3.5 Sonnet"
            ),
        ]
        
        # OpenAI Models (ordered by preference/recency)
        openai_models = [
            ModelInfo(
                id="gpt-4o",
                name="GPT-4o",
                provider="openai",
                release_date="2024-05-13",
                capabilities=["tools", "vision", "analysis"],
                context_length=128000,
                is_default=True,
                description="Latest GPT-4 Omni model"
            ),
            ModelInfo(
                id="gpt-4o-mini",
                name="GPT-4o Mini",
                provider="openai",
                release_date="2024-07-18",
                capabilities=["tools", "fast"],
                context_length=128000,
                description="Smaller, faster GPT-4o model"
            ),
            ModelInfo(
                id="gpt-4-turbo",
                name="GPT-4 Turbo",
                provider="openai",
                release_date="2024-04-09",
                capabilities=["tools", "vision"],
                context_length=128000,
                description="GPT-4 Turbo with vision"
            ),
            ModelInfo(
                id="gpt-4",
                name="GPT-4",
                provider="openai",
                release_date="2023-03-14",
                capabilities=["tools"],
                context_length=8192,
                description="Original GPT-4 model"
            ),
            ModelInfo(
                id="gpt-3.5-turbo",
                name="GPT-3.5 Turbo",
                provider="openai",
                release_date="2022-11-30",
                capabilities=["tools", "fast"],
                context_length=16385,
                description="Fast and efficient GPT-3.5"
            ),
        ]
        
        # Mock Models
        mock_models = [
            ModelInfo(
                id="default",
                name="Default Mock",
                provider="mock",
                capabilities=["testing"],
                is_default=True,
                description="Mock model for testing"
            ),
        ]
        
        # Add all models to registry
        for model_list in [anthropic_models, openai_models, mock_models]:
            for model in model_list:
                models[model.id] = model
        
        return models
    
    def get_models_by_provider(self, provider: str, include_deprecated: bool = False) -> List[ModelInfo]:
        """Get all models for a specific provider."""
        models = [
            model for model in self._models.values()
            if model.provider == provider and (include_deprecated or not model.is_deprecated)
        ]
        # Sort by default first (defaults come first), then by release date (newest first)
        def sort_key(m):
            # Default models get priority 0, non-defaults get priority 1
            default_priority = 0 if m.is_default else 1
            # For release date, we want newer dates first
            # Convert date string to a comparable format, with None/empty dates getting lowest priority
            date_str = m.release_date or "0000-00-00"
            # Negate the date string for reverse chronological order (newer dates first)
            # We'll use a tuple that sorts newer dates first
            try:
                # Parse date and convert to negative timestamp for reverse sorting
                from datetime import datetime
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                # Use negative timestamp so newer dates sort first
                date_priority = -date_obj.timestamp()
            except (ValueError, TypeError):
                # If date parsing fails, use a very low priority (old date)
                date_priority = float('inf')

            # Add model ID as final tiebreaker for consistent ordering
            return (default_priority, date_priority, m.id)

        return sorted(models, key=sort_key)
    
    def get_model_ids_by_provider(self, provider: str, include_deprecated: bool = False) -> List[str]:
        """Get model IDs for a specific provider."""
        return [model.id for model in self.get_models_by_provider(provider, include_deprecated)]
    
    def get_model_choices_by_provider(self, provider: str, include_deprecated: bool = False) -> List[Dict[str, str]]:
        """Get model choices (id, name) for a specific provider."""
        return [
            {"id": model.id, "name": model.name}
            for model in self.get_models_by_provider(provider, include_deprecated)
        ]
    
    def get_default_model(self, provider: str) -> Optional[str]:
        """Get the default model ID for a provider."""
        models = self.get_models_by_provider(provider)
        for model in models:
            if model.is_default:
                return model.id
        # Fallback to first model if no default is set
        return models[0].id if models else None
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self._models.get(model_id)
    
    def get_all_providers(self) -> List[str]:
        """Get all available providers."""
        providers = set(model.provider for model in self._models.values())
        return sorted(providers)


# Global model registry instance
model_registry = ModelRegistry()


# Convenience functions for backward compatibility
def get_anthropic_models(include_deprecated: bool = False) -> List[str]:
    """Get list of Anthropic model IDs."""
    return model_registry.get_model_ids_by_provider("anthropic", include_deprecated)


def get_openai_models(include_deprecated: bool = False) -> List[str]:
    """Get list of OpenAI model IDs."""
    return model_registry.get_model_ids_by_provider("openai", include_deprecated)


def get_mock_models() -> List[str]:
    """Get list of mock model IDs."""
    return model_registry.get_model_ids_by_provider("mock")


def get_default_model(provider: str) -> Optional[str]:
    """Get default model for a provider."""
    return model_registry.get_default_model(provider)


# Constants for backward compatibility
DEFAULT_ANTHROPIC_MODEL = get_default_model("anthropic")
DEFAULT_OPENAI_MODEL = get_default_model("openai")
DEFAULT_MOCK_MODEL = get_default_model("mock")
